-- 1、员工信息表
CREATE TABLE `employee` (
                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                            `staff_id` int(11) unsigned DEFAULT '0' COMMENT '员工工号',
                            `name` varchar(20) NOT NULL DEFAULT '' COMMENT '中文姓名',
                            `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '电话号码',
                            `area` varchar(32) NOT NULL DEFAULT '' COMMENT '工作地区, 例如北京、上海等',
                            `gender` tinyint(1) NOT NULL DEFAULT '1' COMMENT '性别, 1 男 2 女',
                            `is_valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:在职 2:离职',
                            PRIMARY KEY (`id`),
                            INDEX `idx_staff_id` (`staff_id`) COMMENT '员工工号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='员工信息表';

-- 2、员工假期表
CREATE TABLE `holiday` (
                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                           `staff_id` int(11) unsigned DEFAULT '0' COMMENT '员工工号',
                           `sick_num` int(10) NOT NULL DEFAULT '0' COMMENT '病假剩余天数',
                           `annual_num` int(10) NOT NULL DEFAULT '0' COMMENT '年假剩余天数',
                           PRIMARY KEY (`id`),
                           INDEX `idx_staff_id` (`staff_id`) COMMENT '员工工号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='员工假期表';

-- 3、员工请假表
CREATE TABLE `leave_holiday` (
                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                 `staff_id` int(11) unsigned DEFAULT '0' COMMENT '员工工号',
                                 `start_date` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '开始时间',
                                 `end_date` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '结束时间',
                                 `day_num` int(20) NOT NULL DEFAULT '0' COMMENT '请假天数',
                                 `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:年假 2:病假',
                                 `area` varchar(32) NOT NULL DEFAULT '' COMMENT '工作地区, 例如北京、上海等',
                                 PRIMARY KEY (`id`),
                                 INDEX `idx_staff_id` (`staff_id`) COMMENT '员工工号索引',
                                 INDEX `idx_start_date` (`start_date`) COMMENT '开始时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='员工请假表';