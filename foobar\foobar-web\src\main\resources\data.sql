-- 文件: data.sql
-- 描述: 数据库初始化数据

SET NAMES utf8;

-- 插入员工信息
INSERT INTO `employee` (`id`, `staff_id`, `name`, `mobile`, `area`, `gender`, `is_valid`) VALUES
                                                                                              ('1', '1', '赵四', '13111111111', '北京', '1', '1'),
                                                                                              ('2', '2', '张三', '15122222222', '北京', '1', '1'),
                                                                                              ('3', '3', '李四', '18910082222', '上海', '2', '2'),
                                                                                              ('4', '4', '张方', '15110072222', '上海', '1', '1'),
                                                                                              ('5', '5', '小明', '15110062222', '上海', '1', '1'),
                                                                                              ('6', '6', '旺财', '15110052222', '上海', '1', '1'),
                                                                                              ('7', '7', '翠花', '15110042222', '上海', '1', '1');

-- 插入员工假期额度
INSERT INTO `holiday` (`id`, `staff_id`, `sick_num`, `annual_num`) VALUES
                                                                       ('1', '1', '10', '10'),
                                                                       ('2', '2', '8', '8'),
                                                                       ('3', '3', '6', '10'),
                                                                       ('4', '4', '10', '6'),
                                                                       ('5', '5', '10', '10'),
                                                                       ('6', '6', '10', '10'),
                                                                       ('7', '7', '10', '10');

-- 插入员工请假记录
INSERT INTO `leave_holiday` (`id`, `staff_id`, `start_date`, `end_date`, `day_num`, `type`, `area`) VALUES
                                                                                                        ('1', '2', '2025-01-16 00:00:00', '2025-01-17 00:00:00', '2', '1', '北京'),
                                                                                                        ('2', '4', '2025-03-04 00:00:00', '2025-03-07 00:00:00', '4', '1', '上海'),
                                                                                                        ('3', '2', '2025-01-21 00:00:00', '2025-01-22 00:00:00', '2', '2', '北京'),
                                                                                                        ('4', '3', '2025-04-21 00:00:00', '2025-04-24 00:00:00', '4', '2', '北京');